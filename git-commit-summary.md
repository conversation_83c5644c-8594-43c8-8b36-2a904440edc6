# Git Commit 提交内容总结

## 提交标题
```
fix: 修复会计科目切换和辅助核算传参问题
```

## 提交描述
```
修复 /bookkeeping/review 页面中会计科目切换时辅助核算数据丢失和新增辅助核算数据传递失败的问题

### 问题1: 科目切换时辅助核算数据丢失
- **根本原因**: parseSubjectString 函数在处理科目切换时，当新科目不支持辅助核算时会强制清空辅助核算数据
- **修复方案**: 优化辅助核算数据保留逻辑，只有在科目对象明确指示不支持辅助核算时才清空数据
- **影响**: 确保用户切换科目时不会意外丢失辅助核算信息

### 问题2: 新增辅助核算数据传递失败  
- **根本原因**: parseSubjectString 函数没有检查 subjectObject.assistantOptions 中的辅助核算数据
- **修复方案**: 在 parseSubjectString 函数开头添加对 subjectObject.assistantOptions 的检查和处理逻辑
- **数据流程**: AddAuxiliaryPop → VoucherEditing.listenAuxiliaryMitt → subjectObject.assistantOptions → parseSubjectString → vouchers/update API

### 技术改进
- 增强 parseSubjectString 函数对科目对象中辅助核算选项的支持
- 优化辅助核算数据保留策略，采用更智能的数据保留逻辑
- 完善调试日志，便于问题排查和数据流程追踪

### 测试验证
- ✅ 科目切换时辅助核算数据正确保留
- ✅ 新增辅助核算数据正确传入 vouchers/update 接口
- ✅ 调试日志完整，便于问题排查
```

## 修改文件列表
```
M apps/web-antd/src/views/jsj-ai/voucher/bookkeeping/review/index.vue
M CHANGELOG.md
A test-auxiliary-fix.md
```

## 关键代码变更

### 1. 新增对 assistantOptions 的支持
```typescript
// 检查科目对象中是否包含新增的辅助核算信息
if (subjectObject && subjectObject.assistantOptions && subjectObject.assistantOptions.length > 0) {
  console.log('✅ 检测到科目对象中包含辅助核算选项:', subjectObject.assistantOptions);
  
  // 从科目文本中解析科目代码和名称
  const parts = subjectText.split(' ').filter(Boolean);
  let accountCode = '';
  let accountName = '';
  
  if (parts.length >= 2) {
    accountCode = parts[0] || '';
    accountName = parts[1] || '';
  }
  
  // 使用科目对象中的辅助核算选项
  const auxiliaryInfo = subjectObject.assistantOptions;
  
  return {
    accountCode,
    accountId,
    accountName,
    auxiliaryInfo,
  };
}
```

### 2. 优化辅助核算数据保留逻辑
```typescript
// 优先保留原始辅助核算数据，除非明确知道新科目不支持辅助核算
let auxiliaryInfo = originalAuxiliary;

// 如果科目对象明确指示不支持辅助核算，才清空辅助核算数据
if (subjectObject && subjectObject.useAssistant === false) {
  auxiliaryInfo = [];
} else if (!subjectUseAssistant && originalAuxiliary.length === 0) {
  // 只有当新科目不支持辅助核算且原始数据也没有辅助核算时，才设为空数组
  auxiliaryInfo = [];
} else {
  // 其他情况都保留原始辅助核算数据
  auxiliaryInfo = originalAuxiliary;
}
```

## 影响范围
- `/bookkeeping/review` 页面的会计科目切换功能
- 新增辅助核算功能的数据传递
- `vouchers/update` 接口的 `auxiliary` 参数传递
- 凭证保存时的辅助核算数据完整性

## 向后兼容性
- ✅ 完全向后兼容，不影响现有功能
- ✅ 保持原有 API 接口格式不变
- ✅ 现有的辅助核算数据处理逻辑继续有效
